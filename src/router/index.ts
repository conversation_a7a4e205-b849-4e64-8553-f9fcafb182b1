import { route } from 'quasar/wrappers';
import {
  createM<PERSON>oryHistory,
  createRouter,
  createWebHashHistory,
  createWebHistory,
} from 'vue-router';

import routes from './routes';
import { useTrackData } from '@composables';
import { USER } from '@repositories';

/*
 * If not building with SSR mode, you can
 * directly export the Router instantiation;
 *
 * The function below can be async too; either use
 * async/await or return a Promise which resolves
 * with the Router instance.
 */

export default route(function (/* { store, ssrContext } */) {
  const createHistory = process.env.SERVER
    ? createMemoryHistory
    : process.env.VUE_ROUTER_MODE === 'history'
    ? createWebHistory
    : createWebHashHistory;

  const router = createRouter({
    scrollBehavior: () => ({ left: 0, top: 0 }),
    routes,

    // Leave this as is and make changes in quasar.conf.js instead!
    // quasar.conf.js -> build -> vueRouterMode
    // quasar.conf.js -> build -> publicPath
    history: createHistory(process.env.VUE_ROUTER_BASE),
  });

  router.beforeEach(async (to, _from, next) => {
    const { track } = useTrackData();

    const PATH = [
      'vote',
      'geo',
      'students',
      'tt',
      'fb',
      'ig',
      'tele',
      'x',
      'web',
      'reen',
      'yuni',
      'jess',
      'playinsg',
      'hall10',
      'hall16',
      'crescenthall',
      'hall8',
      'pioneerhall',
      'hall12',
      'hall7',
      'raffleshall',
      'hall15',
      'email',
      'hall14',
      'hall1',
      'saracahall',
      'hunt',
      'sonar',
      // HTM 1M
      'ooh',
      'oohpsdAJN',
      'oohpsdBB',
      'oohpsdCBR',
      'oohpsdDKT',
      'oohpsdFR',
      'oohpsdHV',
      'oohpsdKTB',
      'oohpsdLS',
      'oohpsdMPS',
      'oohpsdMSL',
      'oohpsdON',
      'oohpsdTS',
      'oohpsdTB',
      'oohpsdYT',
      'ooh4s',
      'ooh6s',
      'll',
      'oohwink',
      'pp',
      'ppcounter',
      'ppstandee',
      'ppcards',
      'sqfb',
      'sqig',
      'sqtt',
      'sqtele',
      'pr',
      'partipost',
      'gold',
      'silver',

      // sentosa
      'sentosa',
      'ppsen',
      'pptentsen',
      'ppscratchsen',
      'ppcardsen',
      'prsen',
      'crystal',
      'sentosasilver',

      // Capitaland
      'interstatial',

      // DBS
      'dbs',
    ];

    const path = to.redirectedFrom?.path?.replace('/', '') || '';
    if (PATH.includes(path))
      USER.trackData('custom-path', {
        path: path,
      });

    track('page_visit');
    track('version', {
      version: process.env.BUILD_VERSION,
    });

    if (to.path !== '/') return next('/');

    return next();
  });

  return router;
});
