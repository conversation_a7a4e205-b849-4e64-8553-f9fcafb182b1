<script lang="ts" setup>
import {
  IRiveViewModel,
  useBrandActions,
  useHuntingStop,
  useRive,
  useTick,
} from '@composables';
import { HuntingStop } from '@types';
import { Fit, Alignment, Layout } from '@rive-app/canvas';
import { useBAStore } from '@stores';
import { BrandActionItem } from '@components';
import {
  HUNTINGSTOPREWARDINFO,
  HuntingStopRewardType,
  HuntingStopType,
} from '@constants';
import gsap from 'gsap';
import { timeCountDown } from '@helpers';

interface Props {
  data: HuntingStop;
}

const props = defineProps<Props>();

const tl = gsap.timeline();
const storeBA = useBAStore();
const brandHooks = useBrandActions();

const { newBrandActions } = storeToRefs(storeBA);
const { push } = useMicroRoute();
const { t } = useI18n();
const { now } = useTick();
const {
  alertMessage,
  errorMessage,
  hasErrorMsg,
  rewardAssetNames,
  claimReward,
  getHuntingStopInfo,
} = useHuntingStop();

const huntingStop = ref(props.data);
const stallRef = ref<HTMLCanvasElement | null>(null);
const dialogMessage = ref('');
const dialogDetail = ref(false);
const rewardDetailModel = ref<{
  name: string;
  icon: HuntingStopRewardType;
  qty: number;
  des: string;
  btnTxt: string;
  action?: () => void;
}>({
  name: '',
  icon: HuntingStopRewardType.CRYSTAL,
  qty: 0,
  des: '',
  btnTxt: '',
  action: undefined,
});

const brandActionByUniqueId = computed(() => {
  return newBrandActions.value.filter(
    (item) => item.brand_unique_id === huntingStop.value.brand_unique_id
  );
});

const randomBrandAction = computed(() => {
  if (!brandActionByUniqueId.value.length) return undefined;
  const randomIndex = Math.floor(
    Math.random() * brandActionByUniqueId.value.length
  );
  return brandActionByUniqueId.value[randomIndex];
});

const countdownReward = computed(() => {
  if (!huntingStop.value.lock_until) return 0;
  return Math.max(0, +new Date(huntingStop.value.lock_until) - now.value);
});

const canUse = computed(() => {
  return countdownReward.value <= 0 && !huntingStop.value.rewards;
});

const isMegaStop = computed(
  () => huntingStop.value.type === HuntingStopType.MEGA
);

const createRiveVMConfig = (
  canU: boolean
): (IRiveViewModel | undefined)[] => {
  const characterType = isMegaStop.value ? 'nancii' : 'sqkii';

  if (isCountdown) {
    return [
      {
        path: 'selectCharacter',
        type: 'enum' as const,
        value: characterType,
      },

      {
        path: 'isDoorOpen',
        type: 'boolean' as const,
        value: true,
      },
      {
        path: 'property of vmCharacter/tickleCounter',
        type: 'number' as const,
        value: 15,
      },
      {
        path: 'property of vmCharacter/beingTickled',
        type: 'boolean' as const,
        value: true,
      },
      ...(isMegaStop.value
        ? [
            {
              path: 'property of vmCharacter/clothDesign',
              type: 'enum' as const,
              value: 'branded',
            },
          ]
        : []),
    ];
  }

  return [
    {
      path: 'selectCharacter',
      type: 'enum' as const,
      value: characterType,
    },
    {
      path: 'isDoorOpen',
      type: 'boolean' as const,
      value: async () => {
        const reward = await claimReward(props.data.unique_id);
        if (!reward) return;

        huntingStop.value = {
          ...huntingStop.value,
          rewards: reward.rewards,
          lock_until: reward.lock_until,
        };
      },
    },
    {
      path: 'property of vmCharacter/tap',
      type: 'trigger' as const,
      value: () => setDialogMessage(props.data.fun_facts),
    },
    ...(isMegaStop.value
      ? [
          {
            path: 'property of vmCharacter/clothDesign',
            type: 'enum' as const,
            value: 'branded',
          },
        ]
      : []),
  ];
};

const vmConfig = computed(
  () => createRiveVMConfig(!canUse.value).filter(Boolean) as IRiveViewModel[]
);

const setDialogMessage = (message: string) => {
  if (!message) {
    return;
  }
  dialogMessage.value = t(message);
};

const getWelcomeText = (): string => {
  const text = isMegaStop.value ? 'MEGASTOP_WELCOME' : 'HUNTINGSTOP_WELCOME';
  if (!canUse.value) return props.data.fun_facts || text;
  return text;
};

const animateDialogMessage = async () => {
  if (!dialogMessage.value || hasErrorMsg.value) {
    return;
  }
  await tl.fromTo(
    '.speech',
    {
      opacity: 0,
      y: 5,
    },
    {
      opacity: 1,
      y: 0,
      delay: 0.8,
      duration: 1,
    }
  );
  // .fromTo(
  //   '#text',
  //   { text: '' },
  //   {
  //     text: dialogMessage.value,
  //     duration: 1.5,
  //     ease: Linear.easeNone,
  //   }
  // );
};

const onRiveLoaded = async () => {
  const text = getWelcomeText();
  setDialogMessage(t(text));
};
const showRewardDetailDialog = (reward: HuntingStopRewardType, qty: number) => {
  rewardDetailModel.value = {
    ...HUNTINGSTOPREWARDINFO[reward],
    qty,
    btnTxt:
      reward === HuntingStopRewardType.CRYSTAL
        ? t('HUNTINGSTOP_REWARD_MORECRYSTAL')
        : t('HUNTINGSTOP_REWARD_INVENTORY'),
    action: () => {
      reward === HuntingStopRewardType.CRYSTAL
        ? push('offer_wall')
        : push('inventory');
      dialogDetail.value = false;
    },
  };
  dialogDetail.value = true;
};

useRive({
  canvas: stallRef,
  src: 'imgs/huntingstop/hunting_stop.riv',
  autoplay: true,
  autoBind: false,
  stateMachines: 'State Machine',
  layout: new Layout({
    fit: Fit.FitWidth,
    alignment: Alignment.BottomCenter,
  }),
  data: vmConfig.value,
  onLoad: onRiveLoaded,
});

watchEffect(() => animateDialogMessage(), { flush: 'post' });

watch(countdownReward, async (newVal) => {
  if (newVal <= 0) {
    const data = await getHuntingStopInfo(props.data.unique_id);
    if (!data) return;
    huntingStop.value = data;
  }
});
</script>

<template>
  <div class="fullscreen stall" :class="{ mega: isMegaStop }">
    <Button
      variant="secondary"
      shape="square"
      class="absolute left-4 top-4 z-50"
      @click="push(-1)"
    >
      <Icon name="arrow-left" />
    </Button>

    <div
      class="location-name truncate absolute left-1/2 -translate-x-1/2 z-10"
      :class="{ mega: isMegaStop }"
      v-html="t(huntingStop.name)"
    />

    <Icon
      v-if="isMegaStop"
      name="/huntingstop/chain"
      class="w-[260px] h-10 z-10 absolute top-0 left-1/2 -translate-x-1/2"
    />

    <Icon
      :name="isMegaStop ? '/huntingstop/dbs-scifi' : '/huntingstop/flag'"
      class="w-[10%] z-[100] absolute bottom-[79vw] left-[15vw] -translate-x-1/2"
      :class="{
        'w-[18%]': isMegaStop,
        'bottom-[81vw]': isMegaStop,
        'left-[12vw]': isMegaStop,
      }"
    />
    <Icon
      :name="
        isMegaStop ? '/huntingstop/purple-diamond' : '/huntingstop/diamond'
      "
      class="w-[25%] z-[100] absolute bottom-[79vw] right-[-5vw] -translate-x-1/2"
      :class="{
        'w-[28%]': isMegaStop,
        'bottom-[81vw]': isMegaStop,
        'right-[-10vw]': isMegaStop,
      }"
    />

    <div
      class="flex w-full flex-col flex-auto h-[calc(100vh-120vw-90px)] px-4 flex-nowrap items-stretch gap-5 absolute left-1/2 -translate-x-1/2 bottom-[120vw] z-50"
      :class="[
        (!canUse && !hasErrorMsg) || hasErrorMsg
          ? 'justify-between'
          : 'justify-end',
      ]"
    >
      <div
        v-if="!canUse && !hasErrorMsg"
        class="banner text-center p-4"
        v-html="
          t('HUNTINGSTOP_NEXT_REWARD', {
            TIME: timeCountDown(countdownReward),
          })
        "
      />
      <!-- Error Banner -->
      <div
        v-if="hasErrorMsg"
        class="banner text-center p-4 error"
        v-html="t(alertMessage || errorMessage)"
      />

      <!-- Speech Bubble -->
      <div class="speech opacity-0" v-if="dialogMessage && !hasErrorMsg">
        <span id="text">
          {{ dialogMessage }}
        </span>
      </div>
    </div>

    <canvas
      ref="stallRef"
      class="w-full h-full absolute bottom-0 left-0 z-10"
      :class="{
        'opacity-0': hasErrorMsg,
        'pointer-events-none': hasErrorMsg || !canUse,
      }"
    />
    <BrandActionItem
      v-if="randomBrandAction && !hasErrorMsg"
      class="!absolute z-50 bottom-[20vw] left-1/2 -translate-x-1/2 w-4/5"
      :class="{ 'opacity-0 pointer-events-none': canUse }"
      :brand-hooks="brandHooks"
      :data="randomBrandAction"
      show-type="twinkle"
      from-sponsor
    />
    <div
      v-if="huntingStop.rewards && !hasErrorMsg"
      class="flex justify-center items-center gap-3 absolute z-50 left-1/2 -translate-x-1/2 bottom-[10vw]"
    >
      <div
        v-for="(value, reward) in huntingStop.rewards"
        :key="reward"
        @click="showRewardDetailDialog(reward, value)"
        class="w-14 h-14 relative flex items-center justify-center"
      >
        <Icon
          :name="rewardAssetNames[reward]"
          class="!w-full object-contain object-center"
          :class="[`hs-${reward}`]"
        />
        <div
          class="absolute bottom-0 text-[18px] font-black text-stroke right-1 text-border"
        >
          {{ value }}
        </div>
      </div>
    </div>
  </div>
  <q-dialog v-model="dialogDetail" persistent full-height full-width>
    <Dialog @close="dialogDetail = false">
      <template #header>
        <div class="text-[14px] text-center font-normal">Reward obtained</div>
        <div class="text-base font-bold">
          {{ rewardDetailModel.name }} x{{ rewardDetailModel.qty }}
        </div>
      </template>
      <div class="flex flex-col items-center justify-center gap-10">
        <div
          class="flex flex-col items-center justify-center -ml-2 flex-nowrap"
        >
          <Icon :name="rewardAssetNames[rewardDetailModel.icon]" :size="120" />
          <div class="text-center text-base font-bold">
            {{ rewardDetailModel.des }}
          </div>
        </div>

        <Button
          class="mx-auto"
          variant="purple"
          :label="rewardDetailModel.btnTxt"
          @click="() => rewardDetailModel.action?.()"
        />
      </div>
    </Dialog>
  </q-dialog>
</template>
<style lang="scss" scoped>
.stall {
  background: url('/imgs/huntingstop/stall-bg.png') no-repeat center center,
    linear-gradient(180deg, #3c1526 8.46%, #8c1946 94.96%), #d9d9d9;
  background-size: 130% auto;

  &.mega {
    background: url('/imgs/huntingstop/stall-bg-mega.png') no-repeat center
        center,
      linear-gradient(180deg, #3c1526 8.46%, #8c1946 94.96%), #d9d9d9;
    background-size: cover;
  }

  &::before {
    width: 126%;
    position: absolute;
    height: 100%;
    background: url('/imgs/huntingstop/stall-top.png') no-repeat top center;
    background-size: 100% auto;
    content: '';
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
  }

  &.mega::before {
    width: 100%;
    position: absolute;
    height: 100%;
    background: url('/imgs/huntingstop/stall-top-mega.png') no-repeat top center;
    background-size: 100% auto;
    content: '';
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
  }

  &::after {
    width: 100%;
    position: absolute;
    height: 130vw;
    background: url('/imgs/huntingstop/stall-bot.png') no-repeat top center;
    background-size: 100% 105%;
    content: '';
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
  }

  &.mega::after {
    width: 100%;
    position: absolute;
    height: 83vw;
    background: url('/imgs/huntingstop/stall-bot-mega.png') no-repeat top center;
    background-size: 110% 110%;
    content: '';
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
  }

  .speech {
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
    padding: 12px;
    padding-bottom: calc(12px + 4vw);
    background: url('/imgs/huntingstop/speech.png') no-repeat center center;
    background-size: 100% 100%;
  }

  .location-name {
    top: 20px;
    border-radius: 8px;
    border: 3px solid #e2b595;
    background: linear-gradient(198deg, #bb7d54 28.64%, #88502b 119.57%);
    padding: 10px 60px;
    font-size: 16px;
    font-weight: 700;

    &.mega {
      position: relative;
      background: url('/imgs/huntingstop/banner.png') no-repeat center center;
      background-size: 100% 100%;
      border: none;
      color: black;
      height: 54px;
      width: 300px;
      text-align: center;
      padding: 12px 24px 18px;
      top: 40px;
    }
  }

  .banner {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    width: 100%;
    min-height: 64px;
    background: url('/imgs/huntingstop/alert.png') no-repeat center center;
    background-size: 100% 100%;
    z-index: 20;

    &.error {
      background: url('/imgs/huntingstop/error-alert.png') no-repeat center
        center;
      background-size: 100% 100%;
    }
  }
}
.hs-crystal {
  width: 80% !important;
  height: auto;
}
.text-stroke {
  -webkit-text-stroke: 1px #41063c;
  text-stroke: 1px #000;
}
</style>
