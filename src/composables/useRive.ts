import { EventCallback, Rive, RiveParameters } from '@rive-app/canvas';

export interface IRiveViewModel {
  path: string;
  type: 'enum' | 'boolean' | 'number' | 'trigger';
  value: string | boolean | number | EventCallback;
}

export interface UseRiveOptions extends Omit<RiveParameters, 'canvas'> {
  canvas: Ref<HTMLCanvasElement | null>;
  data?: IRiveViewModel[];
}

export interface UseRiveReturn {
  bindViewModelInstance: (data: IRiveViewModel[]) => void;
  cleanup: () => void;
  isInitialized: Readonly<Ref<boolean>>;
}

export function useRive(options: UseRiveOptions): UseRiveReturn {
  let rive: Rive | null = null;
  const isInitialized = ref(false);

  function init(): void {
    if (!options.canvas.value) {
      console.error('Canvas element is not defined');
      return;
    }

    try {
      rive = new Rive({
        ...options,

        canvas: options.canvas.value,
        onLoad: (params) => {
          if (!rive) return;
          rive.resizeDrawingSurfaceToCanvas();
          options.onLoad?.(params);
          _bindViewModelInstance(options.data || []);
          isInitialized.value = true;
        },
        onLoadError: (error) => {
          console.error('Failed to load Rive file:', error);
          options.onLoadError?.(error);
        },
      });
    } catch (error) {
      console.error('Failed to initialize Rive:', error);
    }
  }

  function bindViewModelInstance(data: IRiveViewModel[]): void {
    const vmi = rive?.viewModelInstance;

    if (!vmi) {
      console.error('No default ViewModelInstance found');
      return;
    }
    data.forEach((item) => {
      try {
        const viewModelProperty = vmi[item.type]?.(item.path);
        if (!viewModelProperty) {
          console.warn(
            `ViewModel property not found: ${item.type}(${item.path})`
          );
          return;
        }

        if (typeof item.value === 'function') {
          viewModelProperty.on(item.value);
        } else {
          if (item.type !== 'trigger' && 'value' in viewModelProperty) {
            viewModelProperty.value = item.value;
          }
        }
      } catch (error) {
        console.error(`Error binding ${item.type}(${item.path}):`, error);
      }
    });
  }

  function _bindViewModelInstance(data: IRiveViewModel[]): void {
    if (!rive) {
      console.error('Rive instance not initialized');
      return;
    }

    const vm = rive.defaultViewModel();
    if (!vm) {
      console.error('No default ViewModel found');
      return;
    }

    const vmi = vm.defaultInstance();

    if (!vmi) {
      console.error('No default ViewModelInstance found');
      return;
    }
    data.forEach((item) => {
      try {
        const viewModelProperty = vmi[item.type]?.(item.path);
        if (!viewModelProperty) {
          console.warn(
            `ViewModel property not found: ${item.type}(${item.path})`
          );
          return;
        }

        if (typeof item.value === 'function') {
          viewModelProperty.on(item.value);
        } else {
          if (item.type !== 'trigger' && 'value' in viewModelProperty) {
            viewModelProperty.value = item.value;
          }
        }
      } catch (error) {
        console.error(`Error binding ${item.type}(${item.path}):`, error);
      }
    });

    rive.bindViewModelInstance(vmi);
  }

  function cleanup(): void {
    if (rive) {
      rive.cleanup();
      rive = null;
      isInitialized.value = false;
    }
  }

  onMounted(async () => {
    await nextTick();
    init();
  });

  onUnmounted(() => {
    cleanup();
  });

  return {
    bindViewModelInstance,
    cleanup,
    isInitialized: readonly(isInitialized),
  };
}
