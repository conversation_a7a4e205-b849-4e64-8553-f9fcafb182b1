<script lang="ts" setup>
import {
  playSFX,
  useGlobal,
  useInventory,
  useState,
  useTick,
  useTrackData,
} from '@composables';
import { USER } from '@repositories';
import { useBAStore, useDialogStore, useMapStore, useUserStore } from '@stores';
import {
  ToolKitButton,
  TimedMission,
  DevToolHud,
  DynamicButton,
} from '@components';
import type { IGlobeState, IViableMapLayer } from '@types';
import dayjs from 'dayjs';
import 'swiper/css';
import 'swiper/css/pagination';
import { random } from 'lodash';

const storeUser = useUserStore();
const storeGlobal = useDialogStore();
const storeBA = useBAStore();
const storeMap = useMapStore();
const storeDialog = useDialogStore();

const { geoState, _zoom } = storeToRefs(storeMap);
const {
  isSeasonStarting,
  user,
  currentSeason,
  events,
  hasGoldenCoin,
  onboarding,
  isEnabledGPS,
  goldenCoinScheduled,
  features,
  currentStep,
  perpetualHunt,
} = storeToRefs(storeUser);
const { highLightedHunt, triggerBagOnboarding } = storeToRefs(storeDialog);
const { triggerTimeMission } = storeToRefs(storeBA);
const { push, openDialog } = useMicroRoute();
const { coinLimitCountdown } = useGlobal();
const { t } = useI18n();
const { now } = useTick();
const { track } = useTrackData();
const { state } = useState<IGlobeState>();
const { showInventoryIcon } = useInventory();
const clickedMapLegend = ref(false);
const mapLegendOnboarding = computed(
  () =>
    // !!onboarding.value?.capitaland_onboarding_journey &&
    !clickedMapLegend.value && !onboarding.value?.shrink_silver_coin
);
const inUsingPowerUp = computed(() => {
  return [
    storeGlobal.showBeaconGUI,
    storeGlobal.showCoinSonarGUI,
    storeGlobal.showMetalDetectorGUI,
    storeGlobal.showSilverCoinSelectCircle,
  ].some(Boolean);
});

const LEGEND_DATA = computed(() => {
  const data = [
    {
      icon: 'golden-circle',
      text: t('LEGEND_GOLDEN_POSSIBLE'),
      permission: hasGoldenCoin.value && !perpetualHunt.value,
    },
    {
      icon: 'eliminated-circle',
      text: t('LEGEND_ELIMINATED_POSSIBLE'),
      permission: hasGoldenCoin.value && !perpetualHunt.value,
    },
    {
      icon: 'silver-circle',
      text: t('LEGEND_SILVER_POSSIBLE', {
        REWARD: currentSeason.value?.silver_reward,
      }),
      permission: true,
    },
  ];
  return data.filter((d) => d.permission);
});

const MAP_LAYER_DATA = computed(() => {
  return [
    {
      icon: 'legend_all',
      text: t('LEGEND_ALL'),
      type: 'all',
      permission: hasGoldenCoin.value && !perpetualHunt.value,
    },
    {
      icon: 'legend_golden',
      text: t('LEGEND_GOLDEN'),
      type: 'golden',
      permission: hasGoldenCoin.value && !perpetualHunt.value,
    },
    {
      icon: 'legend_silver',
      text: t('LEGEND_SILVER'),
      type: 'silver',
      permission: true,
    },
  ].filter((l) => l.permission) as Array<{
    icon: string;
    text: string;
    type: IViableMapLayer;
  }>;
});

const limitTime = computed(() => {
  if (!coinLimitCountdown.value || !user.value) return;
  return {
    value: now.value,
    max: +new Date(user.value?.coin_lock_until),
    min: +dayjs(user.value?.coin_lock_until).subtract(30, 'days').toDate(),
  };
});

async function handleSetVisibleMapLayer(type: IViableMapLayer) {
  if (!user.value || user.value.setting.viable_map_layer === type) return;
  track('toggle_layer', {
    layer: type,
  });
  track('map_layer_intro', {
    action: type,
  });
  storeUser.updateUser({
    setting: {
      ...user.value?.setting,
      viable_map_layer: type,
    },
  });
  await USER.updateUserSettings({
    type: 'viable_map_layer',
    value: type,
  });
}

function handleOpenBag() {
  storeUser.fetchInventory();
  playSFX('button');
  storeDialog.triggerBagOnboarding = false;
  storeGlobal.showInventoryQuickAccess = true;
  track('home_screen', {
    button: 'bag',
  });
}

function testHuntingStop() {
  const isMega = random(0, 1) >= 0.5;
  const isTrial = LocalStorage.getItem(
    isMega ? 'huntingstop_trial_mega' : 'huntingstop_trial'
  );
  console.log('isTrial', isTrial, isMega);
  push(!isTrial ? 'trial_stall' : 'stall', {
    isMega,
  });
}
</script>

<template>
  <div class="gui" v-if="!inUsingPowerUp">
    <div
      class="toggle-popup"
      :class="{
        'map-legend-onboarding': mapLegendOnboarding,
        expanded: storeGlobal.toggle,
        'opacity-50': highLightedHunt,
      }"
      v-if="isSeasonStarting"
    >
      <div
        class="toggle-popup-bottom"
        :class="{
          '-bottom-2': !hasGoldenCoin,
          'bottom-0': hasGoldenCoin,
        }"
        @click="
          storeGlobal.toggle = !storeGlobal.toggle;
          clickedMapLegend = true;
          track('minimize_header', {
            interaction: !storeGlobal.toggle ? 'minimize' : 'maximize',
          });
          track('home_screen', {
            button: 'map_layer_legend',
            interaction: !storeGlobal.toggle ? 'minimize' : 'maximize',
          });
        "
      >
        <Icon
          class="toggle"
          :class="[storeGlobal.toggle && !mapLegendOnboarding && 'rotate']"
          name="toggle"
          :size="14"
        />
      </div>

      <div class="pt-[8px] px-[20px] pb-[20px]">
        <div v-for="item in LEGEND_DATA" :key="item.icon">
          <div
            class="flex items-center gap-2 mt-2 mb-1 flex-nowrap"
            v-if="item.permission"
          >
            <Icon :name="item.icon" :size="20" />
            <div class="text-sm" v-html="item.text"></div>
          </div>
        </div>
        <template v-if="hasGoldenCoin">
          <div class="w-[50px] h-[1px] bg-[#11D1F9] my-3 mx-auto"></div>
          <!-- <div
            class="mb-3 text-sm font-bold text-center"
            v-html="t('LEGEND_VISIBLE_MAP_LAYER')"
          ></div> -->
          <div class="flex items-center justify-center gap-5 mb-3">
            <div
              class="flex flex-col items-center gap-2"
              v-for="item in MAP_LAYER_DATA"
              :key="item.icon"
            >
              <div
                class="size-10"
                :class="{
                  'border-2 border-[#11D1F9]':
                    item.type === user?.setting.viable_map_layer,
                }"
                @click="handleSetVisibleMapLayer(item.type)"
              >
                <Icon :name="item.icon" class="w-full h-full" />
              </div>
              <div class="text-sm" v-html="item.text"></div>
            </div>
          </div>
        </template>
        <div
          v-if="goldenCoinScheduled && !perpetualHunt"
          class="text-[12px] text-[#ffffffa1] px-4 text-center mb-3 italic font-light"
          v-html="t('LEGEND_TOGGLE_HIDDEN_GOLDEN')"
        ></div>
      </div>
    </div>

    <div
      class="fixed flex flex-col gap-5 gui-top-left top-7 left-5 z-10"
      v-if="!inUsingPowerUp"
    >
      <!-- Pedometer feature -->
      <div
        v-if="features?.pedometer"
        class="rounded-[5px] py-1.5 px-3 flex items-center gap-2 w-max"
        style="background: linear-gradient(180deg, #2a084f 0%, #5a1b99 100%)"
        @click="
          track('home_screen', {
            button: 'pedometer',
          });
          state.triggerPedometerFromGUI = true;
        "
      >
        <img
          :src="`/icons/${
            user?.setting.pedometer ? 'step_running' : 'bi_person-standing'
          }.png`"
          :width="user?.setting.pedometer ? 12 : 20"
        />
        <p
          :class="
            !!currentStep &&
            (user?.setting.pedometer_goal || 0) <= currentStep &&
            'text-[#54D6E2]'
          "
        >
          {{ currentStep }}
        </p>
      </div>

      <TimedMission v-if="triggerTimeMission" class="mt-4" />
    </div>
    <div
      class="fixed flex flex-col items-end gap-2 gui-top-right top-6 right-5"
    >
      <TestingComponent>
        <div class="bg-red-600/50 p-2 font-bold rounded-md">
          Zoom: {{ _zoom.toFixed(2) }}
        </div>
      </TestingComponent>
      <Button
        shape="square"
        @click="
          push('menu_v2');
          track('home_screen', {
            button: 'home_menu',
          });
        "
      >
        <Icon name="menu" :size="15" />
        <div
          v-if="events && events.event_updates.some((e) => !e.seen)"
          class="absolute top-0 right-0 dot"
        ></div>
      </Button>
      <Button
        shape="square"
        @click="storeMap.triggerGPS"
        class="relative"
        v-if="
          isEnabledGPS &&
          !['WAITING_ACTIVE', 'OFF', 'UNAVAILABLE'].includes(geoState)
        "
      >
        <Icon v-if="geoState === 'BACKGROUND'" name="gps-off" />
        <Icon v-else name="gps-on" />
      </Button>

      <div
        v-if="coinLimitCountdown && false"
        class="flex flex-col items-end gap-1"
      >
        <Button shape="square" @click="openDialog('coin_limit')">
          <q-circular-progress
            :min="limitTime?.min"
            :max="limitTime?.max"
            :value="limitTime?.value"
            :thickness="0.35"
            size="20px"
            track-color="red-5"
            color="white"
          />
          <div
            class="absolute top-0 right-0 w-[10px] h-[10px] rounded-full bg-[#ff4242]"
          ></div>
        </Button>
        <div class="text-xs opacity-80" v-html="coinLimitCountdown?.auto"></div>
      </div>
      <TestingComponent>
        <DevToolHud />
      </TestingComponent>
    </div>
    <div
      class="gui-bottom-right flex flex-col gap-4 fixed bottom-[50px] right-0"
    >
      <DynamicButton />

      <!-- <div class="dynamic_btn relative" @click="testHuntingStop">
        <div
          class="fit 'gps-pulse' leading-normal gap-0.5 flex-center column relative"
        >
          <Icon name="icons/stall-icon" :size="20" class="mr-[-6px]" />
          <div class="font-bold text-[10px] leading-[1] text-center mr-[-6px]">
            Hunting <br />
            stop
          </div>
        </div>
      </div> -->

      <div
        class="dynamic_btn"
        @click="handleOpenBag"
        :class="{
          'z-[999999] relative': triggerBagOnboarding,
        }"
      >
        <div class="fit leading-normal gap-0.5 flex-center column relative">
          <Icon name="bag" :size="20" class="mr-[-6px]" />
          <div
            class="font-bold text-[10px] text-center mr-[-6px]"
            v-html="t('BAG_DYNAMIC_BTN')"
          ></div>
        </div>
      </div>
      <ToolKitButton
        @click="
          playSFX('button');
          storeGlobal.toggleHunterTool = true;
          track('home_screen', {
            button: 'hunters_toolkit',
          });
        "
      />
      <HunterTool />
    </div>

    <InventoryQuickView />
  </div>
</template>
<style lang="scss" scoped>
.toggle-popup {
  position: fixed;
  z-index: 2;
  left: 30px;
  top: -2px;
  transform: translateY(calc(-100% + 30px));
  transition: transform 0.5s;
  width: calc(100% - 100px);
  height: auto;

  &.hiding {
    transform: translateY(-100%);
  }

  &-bottom {
    position: absolute;
    width: 40vw;
    height: 26px;
    left: 50%;
    transform: translateX(-50%);
    background-image: url('/imgs/toggle-popup-bottom.png');
    background-size: 100% 100%;

    .toggle {
      position: absolute;
      left: 50%;
      top: 60%;
      z-index: 1;
      transform: translate(-50%, -50%) rotate(180deg);
      transition: all 0.5s;

      &.rotate {
        top: 50%;
        transform: translate(-50%, -50%) rotate(0deg);
      }
    }
  }

  &::before {
    content: '';
    position: absolute;
    z-index: -2;
    inset: 0 3% 7% 3%;
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
    backdrop-filter: blur(3px);
  }

  &::after {
    content: '';
    position: absolute;
    inset: 0;
    z-index: -1;
    background-image: url('/imgs/toggle-popup.png');
    background-size: 100% 100%;
  }

  &.map-legend-onboarding {
    animation: map-legend-bouncing 2s infinite;
  }

  &.expanded {
    transform: translateY(0) !important;
    animation: none;
  }
}

.wrapper-metal-sonar {
  margin-right: -12px;
  margin-top: 10px;
  border-radius: 8px 0px 0px 8px;
}

.metal-sonar-pulse {
  width: 68px;
  height: 72px;
  z-index: 2;
  background: rgba(52, 172, 224, 0.3);
  animation: pulse-sonar 2s infinite;
  box-shadow: 0 0 0 0 rgba(52, 172, 224, 1);
}

@keyframes pulse-sonar {
  0% {
    box-shadow: 0 0 0 0 rgba(52, 172, 224, 0.7);
  }

  60% {
    box-shadow: 0 0 0 10px rgba(52, 172, 224, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(52, 172, 224, 0);
  }
}

.metal-sonar {
  width: 68px;
  padding: 5px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 10px 0px 0px 10px;
  background: rgba(255, 255, 255, 0.3);
  text-align: center;

  &.metal-sonar-border {
    border-radius: 10px 0px 0px 0px;
  }
}

.metal-sonar-results {
  width: 68px;
  top: 0;
  border-radius: 0px 0px 0px 10px;
  background: rgba(91, 91, 91, 0.7);
  padding: 3px 5px;
}

.map-legend-bouncing {
  animation: map-legend-bouncing 1s infinite;
}

@keyframes map-legend-bouncing {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(calc(-100% + 35px));
  }

  40% {
    transform: translateY(calc(-100% + 35px + 10px));
  }

  60% {
    transform: translateY(calc(-100% + 35px + 5px));
  }
}

.dynamic_btn {
  width: 68px;
  height: 68px;
  background: url(/imgs/button/dynamic.png);
  background-size: 100% 100%;
}

.dynamic_btn_bounty {
  width: 68px;
  height: 68px;
  background: url(/imgs/button/dynamic-bounty.png);
  background-size: 100% 100%;
}

.capitaland_highlight {
  position: relative;
  overflow: hidden;
  z-index: 100000;
}

.gui_backdrop_overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 8888;
  pointer-events: none;
  clip-path: polygon(
    0% 0%,
    100% 0%,
    100% calc(100% - 143px - 68px),
    calc(100vw - 8px - 68px) calc(100% - 143px - 68px),
    calc(100vw - 8px - 68px) calc(100% - 125px),
    100% calc(100% - 125px),
    100% 100%,
    0% 100%
  );
}

.gps-pulse {
  position: absolute;
  top: 0;
  left: 0;
  width: 35px;
  height: 35px;
  z-index: 2;
  transform: scale(1);
  background: rgba(52, 172, 224, 0.3);
  animation: pulse-blue 2s infinite;
  box-shadow: 0 0 0 0 rgba(52, 172, 224, 1);
  border-radius: 8px;
}

@keyframes pulse-blue {
  0% {
    transform: scale(1.05);
    box-shadow: 0 0 0 0 rgba(52, 172, 224, 0.7);
  }

  60% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(52, 172, 224, 0);
  }

  100% {
    transform: scale(1.05);
    box-shadow: 0 0 0 0 rgba(52, 172, 224, 0);
  }
}
</style>
<style lang="scss">
.slide-sentosa-golden-coin {
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  top: 45px;
  width: 194px;
  height: 70px;

  .crystal-coin-banner {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    // position: fixed;
    // left: 50%;
    // transform: translateX(-50%);
    // top: 45px;
    width: 194px;
    height: 56px;
    background-size: 100% 100%;

    &.ongoing {
      background-image: url('/imgs/map/free-popup.png');
    }

    &.verifying {
      background-image: url('/imgs/map/verifying-popup-2.png');
    }

    &.forfeited {
      background-image: url('/imgs/map/forfeited-popup-2.png');
    }

    &.found {
      background-image: url('/imgs/map/found-popup-2.png');
      height: 70px !important;
    }
  }
}
</style>
